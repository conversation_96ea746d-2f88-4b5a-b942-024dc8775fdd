#!/usr/bin/env python3
"""
使用OpenCV测试RTSP连接
"""
import cv2
import time

def test_rtsp_with_opencv(rtsp_url):
    """使用OpenCV测试RTSP连接"""
    print(f"Testing RTSP connection with OpenCV: {rtsp_url}")
    
    cap = cv2.VideoCapture(rtsp_url)
    
    if not cap.isOpened():
        print("❌ Failed to open RTSP stream")
        return False
    
    print("✅ RTSP stream opened successfully")
    
    # 尝试读取几帧
    for i in range(5):
        ret, frame = cap.read()
        if ret:
            print(f"✅ Frame {i+1}: {frame.shape}")
        else:
            print(f"❌ Failed to read frame {i+1}")
            break
        time.sleep(0.5)
    
    cap.release()
    return True

if __name__ == "__main__":
    # 测试不同的RTSP URL
    test_urls = [
        "rtsp://127.0.0.1:8554/stream",
        "rtsp://127.0.0.1:8889/stream",
        "rtsp://localhost:8554/stream"
    ]
    
    for url in test_urls:
        print(f"\n{'='*50}")
        print(f"Testing: {url}")
        print('='*50)
        test_rtsp_with_opencv(url)
        time.sleep(1)
