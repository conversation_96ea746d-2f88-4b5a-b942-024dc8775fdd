#!/usr/bin/env python3
"""
简单的RTSP测试
"""
import cv2

def test_rtsp():
    rtsp_url = "rtsp://127.0.0.1:8554/stream"
    print(f"Testing RTSP: {rtsp_url}")
    
    cap = cv2.VideoCapture(rtsp_url)
    
    if cap.isOpened():
        print("✅ RTSP connection successful!")
        
        # 尝试读取一帧
        ret, frame = cap.read()
        if ret:
            print(f"✅ Frame read successful! Shape: {frame.shape}")
        else:
            print("❌ Failed to read frame")
        
        cap.release()
        return True
    else:
        print("❌ Failed to connect to RTSP stream")
        return False

if __name__ == "__main__":
    test_rtsp()
