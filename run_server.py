#!/usr/bin/env python3
"""
直接运行服务器的脚本
"""
import sys
import os

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    import uvicorn
    from main import app
    
    print("Starting FastAPI Video Streaming Service...")
    print("Server will be available at: http://127.0.0.1:8000")
    print("Debug page: http://127.0.0.1:8000/debug")
    print("Press Ctrl+C to stop the server")
    
    uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info")
