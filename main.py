import os
import logging
from fastapi import Fast<PERSON><PERSON>, Request, Response
from fastapi.responses import StreamingResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from camera_manager import CameraManager
from stream_processor import StreamProcessor
from websocket import WebSocket

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

app = FastAPI(title="Video Streaming Service", version="1.0")

# 配置静态文件和模板
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# 初始化摄像头管理器和流处理器
camera_manager = CameraManager()
stream_processor = StreamProcessor(camera_manager)


# 启动时加载示例摄像头
@app.on_event("startup")
async def startup_event():
    # 添加示例摄像头 - 实际应用中应从数据库加载
    rtsp_url = "rtsp://127.0.0.1:8554/stream"
    camera_manager.add_camera(
        camera_id=1,
        name="Front Entrance",
        rtsp_url=rtsp_url
    )

    # 测试 OpenCV 是否可用
    try:
        import cv2
        logging.info("OpenCV version: %s", cv2.__version__)

        # 快速测试 RTSP 连接
        cap = cv2.VideoCapture(rtsp_url)
        if cap.isOpened():
            logging.info("✅ RTSP connection test successful!")
            cap.release()
        else:
            logging.warning("❌ RTSP connection test failed!")
    except ImportError:
        logging.error("❌ OpenCV not available!")
    except Exception as e:
        logging.error("❌ Error testing RTSP: %s", str(e))

    logging.info("Service started with %d cameras", len(camera_manager.cameras))
    logging.info("Access the debug page at: http://127.0.0.1:8000/debug")


# 调试页面 - 不需要API网关
@app.get("/debug")
async def debug_page(request: Request):
    return templates.TemplateResponse(
        "debug.html",
        {"request": request, "cameras": camera_manager.get_all_cameras()}
    )


# MJPEG视频流端点
@app.get("/stream/{camera_id}/mjpeg")
async def mjpeg_stream(camera_id: int):
    camera = camera_manager.get_camera(camera_id)
    if not camera:
        return Response(content="Camera not found", status_code=404)

    return StreamingResponse(
        stream_processor.generate_mjpeg_stream(camera_id),
        media_type="multipart/x-mixed-replace;boundary=frame"
    )


# WebRTC信令端点
# @app.websocket("/stream/{camera_id}/webrtc")
# async def webrtc_signaling(websocket: WebSocket, camera_id: int):
#     camera = camera_manager.get_camera(camera_id)
#     if not camera:
#         await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
#         return
#
#     await websocket.accept()
#     await stream_processor.handle_webrtc_connection(websocket, camera_id)


# 摄像头快照
@app.get("/snapshot/{camera_id}")
async def get_snapshot(camera_id: int):
    camera = camera_manager.get_camera(camera_id)
    if not camera:
        return Response(content="Camera not found", status_code=404)

    image_bytes = stream_processor.get_snapshot(camera_id)
    if not image_bytes:
        return Response(content="Snapshot not available", status_code=503)

    return Response(content=image_bytes, media_type="image/jpeg")


# 摄像头管理API
@app.get("/api/cameras")
async def list_cameras():
    return camera_manager.get_all_cameras()


@app.post("/api/cameras")
async def add_camera(camera_data: dict):
    camera_id = camera_data.get("id")
    name = camera_data.get("name")
    rtsp_url = camera_data.get("rtsp_url")

    if not all([camera_id, name, rtsp_url]):
        return {"error": "Missing required fields"}, 400

    camera_manager.add_camera(camera_id, name, rtsp_url)
    return {"status": "success", "camera_id": camera_id}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="127.0.0.1", port=8000)
