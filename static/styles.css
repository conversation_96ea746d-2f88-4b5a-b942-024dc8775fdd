/* FastAPI Video Streaming Service - Debug Interface Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #1a1a1a;
    color: #ffffff;
    line-height: 1.6;
}

header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

header h1 {
    margin-bottom: 1rem;
    font-size: 2rem;
    font-weight: 300;
}

.controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.controls label {
    font-weight: 500;
}

.controls select, .controls button {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    background-color: rgba(255,255,255,0.1);
    color: white;
    cursor: pointer;
    transition: background-color 0.3s;
}

.controls select:hover, .controls button:hover {
    background-color: rgba(255,255,255,0.2);
}

main {
    padding: 2rem;
    min-height: calc(100vh - 200px);
}

.camera-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.camera-card {
    background: #2d2d2d;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    transition: transform 0.3s, box-shadow 0.3s;
}

.camera-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

.camera-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #444;
}

.camera-header h3 {
    color: #ffffff;
    font-size: 1.2rem;
    font-weight: 500;
}

.camera-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.status-indicator.offline {
    background-color: #666;
}

.status-indicator.connecting {
    background-color: #ffa500;
    animation: pulse 1.5s infinite;
}

.status-indicator.active {
    background-color: #4caf50;
}

.status-indicator.error {
    background-color: #f44336;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-text {
    font-size: 0.9rem;
    color: #ccc;
}

.video-container {
    position: relative;
    background: #000;
    border-radius: 4px;
    overflow: hidden;
}

.video-stream {
    width: 100%;
    height: 240px;
    object-fit: cover;
    background: #000;
}

.controls {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.control-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    background: #4caf50;
    color: white;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s;
}

.control-btn:hover {
    background: #45a049;
}

.control-btn:nth-child(2) {
    background: #f44336;
}

.control-btn:nth-child(2):hover {
    background: #da190b;
}

.control-btn:nth-child(3) {
    background: #2196f3;
}

.control-btn:nth-child(3):hover {
    background: #0b7dda;
}

.stream-info {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(255,255,255,0.05);
    border-radius: 4px;
    font-size: 0.9rem;
}

.stream-info h4 {
    margin-bottom: 0.5rem;
    color: #4caf50;
}

.stream-info p {
    margin: 0.25rem 0;
    color: #ccc;
}

footer {
    background: #2d2d2d;
    padding: 1rem 2rem;
    text-align: center;
    color: #888;
    border-top: 1px solid #444;
}

footer p {
    margin: 0.25rem 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .camera-grid {
        grid-template-columns: 1fr;
    }
    
    header {
        padding: 1rem;
    }
    
    .controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    main {
        padding: 1rem;
    }
}
