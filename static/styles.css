/* FastAPI Video Streaming Service - Debug Interface Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

header {
    background: #fff;
    padding: 1rem 2rem;
    border-bottom: 1px solid #ddd;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

header h1 {
    margin-bottom: 1rem;
    font-size: 1.8rem;
    font-weight: normal;
    color: #333;
}

.controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.controls label {
    font-weight: normal;
    color: #666;
}

.controls select, .controls button {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    color: #333;
    cursor: pointer;
    transition: border-color 0.3s;
}

.controls select:hover, .controls button:hover {
    border-color: #007bff;
}

main {
    padding: 2rem;
    min-height: calc(100vh - 200px);
}

.camera-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.camera-card {
    background: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e0e0e0;
    transition: box-shadow 0.3s;
}

.camera-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.camera-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.camera-header h3 {
    color: #333;
    font-size: 1.2rem;
    font-weight: 500;
}

.camera-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.status-indicator.offline {
    background-color: #666;
}

.status-indicator.connecting {
    background-color: #ffa500;
    animation: pulse 1.5s infinite;
}

.status-indicator.active {
    background-color: #4caf50;
}

.status-indicator.error {
    background-color: #f44336;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-text {
    font-size: 0.9rem;
    color: #666;
}

.video-container {
    position: relative;
    background: #000;
    border-radius: 4px;
    overflow: hidden;
}

.video-stream {
    width: 100%;
    height: 240px;
    object-fit: cover;
    background: #000;
}

.controls {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.control-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #007bff;
    color: white;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s;
}

.control-btn:hover {
    background: #0056b3;
}

.control-btn:nth-child(2) {
    background: #dc3545;
    border-color: #dc3545;
}

.control-btn:nth-child(2):hover {
    background: #c82333;
}

.control-btn:nth-child(3) {
    background: #28a745;
    border-color: #28a745;
}

.control-btn:nth-child(3):hover {
    background: #218838;
}

.stream-info {
    margin-top: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 0.9rem;
    border: 1px solid #e9ecef;
}

.stream-info h4 {
    margin-bottom: 0.5rem;
    color: #28a745;
}

.stream-info p {
    margin: 0.25rem 0;
    color: #666;
}

footer {
    background: #fff;
    padding: 1rem 2rem;
    text-align: center;
    color: #666;
    border-top: 1px solid #ddd;
}

footer p {
    margin: 0.25rem 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .camera-grid {
        grid-template-columns: 1fr;
    }

    header {
        padding: 1rem;
    }

    .controls {
        flex-direction: column;
        align-items: stretch;
    }

    main {
        padding: 1rem;
    }
}
