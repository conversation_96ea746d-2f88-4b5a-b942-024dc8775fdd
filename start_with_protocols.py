#!/usr/bin/env python3
"""
启动支持多协议的视频流服务
"""
import sys
import os
import logging

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """检查依赖是否安装"""
    missing_deps = []
    
    try:
        import cv2
        print("✅ OpenCV available")
    except ImportError:
        missing_deps.append("opencv-python")
    
    try:
        import fastapi
        print("✅ FastAPI available")
    except ImportError:
        missing_deps.append("fastapi[all]")
    
    try:
        import uvicorn
        print("✅ Uvicorn available")
    except ImportError:
        missing_deps.append("uvicorn")
    
    # WebRTC 依赖是可选的
    try:
        import aiortc
        print("✅ aiortc available (WebRTC support enabled)")
    except ImportError:
        print("⚠️ aiortc not available (WebRTC support disabled)")
        print("   Run: pip install aiortc")
    
    if missing_deps:
        print(f"\n❌ Missing dependencies: {', '.join(missing_deps)}")
        print("Please install them with:")
        for dep in missing_deps:
            print(f"   pip install {dep}")
        return False
    
    return True

def main():
    """启动服务"""
    print("FastAPI Video Streaming Service with Multi-Protocol Support")
    print("=" * 60)
    
    if not check_dependencies():
        print("\nPlease install missing dependencies first.")
        return
    
    print("\nStarting server...")
    print("Available protocols:")
    print("  - MJPEG: Direct image streaming (always available)")
    print("  - WebRTC: Real-time communication (requires aiortc)")
    print("  - HLS: HTTP Live Streaming (requires FFmpeg)")
    print("\nServer will be available at:")
    print("  - Main page: http://127.0.0.1:8000/debug")
    print("  - API docs: http://127.0.0.1:8000/docs")
    print("\nPress Ctrl+C to stop the server")
    print("=" * 60)
    
    try:
        import uvicorn
        from main import app
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        uvicorn.run(
            app, 
            host="127.0.0.1", 
            port=8000, 
            log_level="info",
            access_log=True
        )
        
    except KeyboardInterrupt:
        print("\n\nServer stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")

if __name__ == "__main__":
    main()
