#!/usr/bin/env python3
"""
测试RTSP连接的脚本
"""
import subprocess
import sys
import time

def test_rtsp_connection(rtsp_url):
    """测试RTSP连接"""
    print(f"Testing RTSP connection to: {rtsp_url}")
    
    # 使用FFmpeg测试连接
    command = [
        "ffmpeg",
        "-rtsp_transport", "tcp",
        "-i", rtsp_url,
        "-t", "5",  # 只测试5秒
        "-f", "null",
        "-"
    ]
    
    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ RTSP connection successful!")
            return True
        else:
            print("❌ RTSP connection failed!")
            print("STDERR:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ RTSP connection timeout!")
        return False
    except FileNotFoundError:
        print("❌ FFmpeg not found! Please install FFmpeg.")
        return False
    except Exception as e:
        print(f"❌ Error testing RTSP: {e}")
        return False

def test_mjpeg_conversion(rtsp_url):
    """测试MJPEG转换"""
    print(f"Testing MJPEG conversion from: {rtsp_url}")
    
    command = [
        "ffmpeg",
        "-rtsp_transport", "tcp",
        "-i", rtsp_url,
        "-loglevel", "warning",
        "-an",
        "-c:v", "mjpeg",
        "-q:v", "3",
        "-vf", "fps=10,scale=640:480",
        "-f", "mjpeg",
        "-t", "3",  # 只测试3秒
        "-"
    ]
    
    try:
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 读取一些数据
        data = process.stdout.read(1024 * 100)  # 读取100KB
        process.terminate()
        
        if data and len(data) > 1000:
            print("✅ MJPEG conversion successful!")
            print(f"Generated {len(data)} bytes of MJPEG data")
            return True
        else:
            print("❌ MJPEG conversion failed!")
            stderr = process.stderr.read().decode()
            print("STDERR:", stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error testing MJPEG conversion: {e}")
        return False

if __name__ == "__main__":
    # 默认测试URL
    test_urls = [
        "rtsp://127.0.0.1:8554/stream",
        "rtsp://127.0.0.1:8889/stream",
        "rtsp://127.0.0.1:554/stream"
    ]
    
    if len(sys.argv) > 1:
        test_urls = [sys.argv[1]]
    
    for url in test_urls:
        print(f"\n{'='*50}")
        print(f"Testing URL: {url}")
        print('='*50)
        
        # 测试基本连接
        if test_rtsp_connection(url):
            # 如果连接成功，测试MJPEG转换
            test_mjpeg_conversion(url)
        
        time.sleep(1)
    
    print("\nTest completed!")
