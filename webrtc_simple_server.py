#!/usr/bin/env python3
"""
极简 WebRTC 服务器，专门解决 SDP 方向问题
"""
import asyncio
import json
import logging
from fastapi import FastAP<PERSON>, WebSocket
from fastapi.responses import HTMLResponse

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI()

# 检查依赖
try:
    from aiortc import RTCPeerConnection, RTCSessionDescription
    from aiortc.contrib.media import MediaPlayer
    WEBRTC_AVAILABLE = True
except ImportError:
    WEBRTC_AVAILABLE = False

@app.get("/")
async def get_page():
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>极简 WebRTC 测试</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            video { width: 640px; height: 480px; background: #000; border: 1px solid #ccc; }
            button { padding: 10px 20px; margin: 5px; font-size: 16px; }
            .log { background: #f5f5f5; padding: 10px; height: 200px; overflow-y: scroll; font-family: monospace; font-size: 12px; }
            .status { padding: 10px; margin: 10px 0; border-radius: 5px; font-weight: bold; }
            .connecting { background: #fff3cd; color: #856404; }
            .connected { background: #d4edda; color: #155724; }
            .error { background: #f8d7da; color: #721c24; }
        </style>
    </head>
    <body>
        <h1>极简 WebRTC 测试</h1>
        <div id="status" class="status connecting">准备就绪</div>
        
        <video id="remoteVideo" autoplay playsinline controls></video>
        <br>
        
        <button onclick="start()">开始连接</button>
        <button onclick="stop()">停止连接</button>
        <button onclick="clearLog()">清除日志</button>
        
        <h3>日志:</h3>
        <div id="log" class="log"></div>

        <script>
            let pc = null;
            let ws = null;
            
            function log(msg) {
                const logEl = document.getElementById('log');
                const time = new Date().toLocaleTimeString();
                logEl.innerHTML += `[${time}] ${msg}\\n`;
                logEl.scrollTop = logEl.scrollHeight;
                console.log(msg);
            }
            
            function setStatus(msg, type) {
                const statusEl = document.getElementById('status');
                statusEl.textContent = msg;
                statusEl.className = `status ${type}`;
            }
            
            function clearLog() {
                document.getElementById('log').innerHTML = '';
            }
            
            async function start() {
                log('开始 WebRTC 连接...');
                setStatus('正在连接...', 'connecting');
                
                // 创建 PeerConnection（最简配置）
                pc = new RTCPeerConnection({
                    iceServers: [
                        { urls: 'stun:stun.l.google.com:19302' }
                    ]
                });
                
                // 事件处理
                pc.ontrack = (event) => {
                    log('收到远程视频流');
                    document.getElementById('remoteVideo').srcObject = event.streams[0];
                    setStatus('视频流已连接', 'connected');
                };
                
                pc.onconnectionstatechange = () => {
                    log(`连接状态变化: ${pc.connectionState}`);
                    if (pc.connectionState === 'connected') {
                        setStatus('WebRTC 连接成功', 'connected');
                    } else if (pc.connectionState === 'failed') {
                        setStatus('连接失败', 'error');
                    }
                };
                
                pc.oniceconnectionstatechange = () => {
                    log(`ICE 连接状态: ${pc.iceConnectionState}`);
                };
                
                // 简化 ICE 处理
                pc.onicecandidate = (event) => {
                    if (event.candidate && ws && ws.readyState === WebSocket.OPEN) {
                        log('发送 ICE 候选');
                        ws.send(JSON.stringify({
                            type: 'ice',
                            candidate: event.candidate
                        }));
                    }
                };
                
                // WebSocket 连接
                ws = new WebSocket('ws://127.0.0.1:8002/ws');
                
                ws.onopen = async () => {
                    log('WebSocket 连接成功');
                    
                    try {
                        // 创建 offer
                        const offer = await pc.createOffer({
                            offerToReceiveVideo: true,
                            offerToReceiveAudio: false
                        });
                        
                        await pc.setLocalDescription(offer);
                        log('设置本地描述成功');
                        
                        // 发送 offer
                        ws.send(JSON.stringify({
                            type: 'offer',
                            sdp: offer.sdp
                        }));
                        log('发送 SDP offer');
                        
                    } catch (error) {
                        log(`错误: ${error}`);
                        setStatus('创建 offer 失败', 'error');
                    }
                };
                
                ws.onmessage = async (event) => {
                    try {
                        const message = JSON.parse(event.data);
                        log(`收到: ${message.type}`);
                        
                        if (message.type === 'answer') {
                            const answer = new RTCSessionDescription({
                                type: 'answer',
                                sdp: message.sdp
                            });
                            
                            await pc.setRemoteDescription(answer);
                            log('设置远程描述成功');
                            
                        } else if (message.type === 'ice') {
                            if (message.candidate) {
                                await pc.addIceCandidate(message.candidate);
                                log('添加 ICE 候选成功');
                            }
                        } else if (message.type === 'error') {
                            log(`服务器错误: ${message.message}`);
                            setStatus('服务器错误', 'error');
                        }
                        
                    } catch (error) {
                        log(`处理消息错误: ${error}`);
                    }
                };
                
                ws.onerror = (error) => {
                    log(`WebSocket 错误: ${error}`);
                    setStatus('连接错误', 'error');
                };
                
                ws.onclose = () => {
                    log('WebSocket 连接关闭');
                    setStatus('连接已断开', 'error');
                };
            }
            
            function stop() {
                log('停止连接');
                
                if (pc) {
                    pc.close();
                    pc = null;
                }
                
                if (ws) {
                    ws.close();
                    ws = null;
                }
                
                document.getElementById('remoteVideo').srcObject = null;
                setStatus('已停止', 'connecting');
            }
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html)

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    if not WEBRTC_AVAILABLE:
        await websocket.close(code=1011)
        return
    
    await websocket.accept()
    logger.info("WebSocket 连接建立")
    
    # 创建 PeerConnection
    pc = RTCPeerConnection()
    
    try:
        # 添加视频轨道（使用测试源）
        try:
            player = MediaPlayer("testsrc=size=640x480:rate=15", format="lavfi")
            video_track = player.video
            pc.addTrack(video_track)
            logger.info("添加测试视频轨道成功")
        except Exception as e:
            logger.error(f"添加视频轨道失败: {e}")
            await websocket.send_json({"type": "error", "message": "无法创建视频源"})
            return
        
        # 处理消息
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)
                msg_type = message.get("type")
                
                logger.info(f"收到消息: {msg_type}")
                
                if msg_type == "offer":
                    # 处理 offer
                    offer = RTCSessionDescription(sdp=message["sdp"], type="offer")
                    await pc.setRemoteDescription(offer)
                    logger.info("设置远程描述成功")
                    
                    # 创建 answer（使用最简单的方式）
                    answer = await pc.createAnswer()
                    await pc.setLocalDescription(answer)
                    logger.info("创建并设置本地描述成功")
                    
                    # 发送 answer
                    await websocket.send_json({
                        "type": "answer",
                        "sdp": pc.localDescription.sdp
                    })
                    logger.info("发送 answer 成功")
                    
                elif msg_type == "ice":
                    # 处理 ICE 候选
                    candidate = message.get("candidate")
                    if candidate:
                        await pc.addIceCandidate(candidate)
                        logger.info("添加 ICE 候选成功")
                        
            except Exception as e:
                logger.error(f"处理消息错误: {e}")
                import traceback
                logger.error(traceback.format_exc())
                break
                
    except Exception as e:
        logger.error(f"WebRTC 错误: {e}")
    finally:
        await pc.close()
        logger.info("连接已关闭")

if __name__ == "__main__":
    import uvicorn
    print("启动极简 WebRTC 服务器...")
    print("访问: http://127.0.0.1:8002")
    uvicorn.run(app, host="127.0.0.1", port=8002)
