#!/usr/bin/env python3
"""
测试 WebRTC 依赖
"""

def test_aiortc():
    """测试 aiortc 安装"""
    try:
        import aiortc
        print(f"✅ aiortc 版本: {aiortc.__version__}")
        
        # 测试基本组件
        from aiortc import RTCPeerConnection, RTCSessionDescription, RTCIceCandidate
        print("✅ RTCPeerConnection 可用")
        print("✅ RTCSessionDescription 可用") 
        print("✅ RTCIceCandidate 可用")
        
        # 测试 MediaPlayer
        try:
            from aiortc.contrib.media import MediaPlayer
            print("✅ MediaPlayer 可用")
        except ImportError as e:
            print(f"❌ MediaPlayer 不可用: {e}")
            
        return True
        
    except ImportError as e:
        print(f"❌ aiortc 未安装: {e}")
        print("安装命令: pip install aiortc")
        return False

def test_basic_webrtc():
    """测试基本 WebRTC 功能"""
    try:
        from aiortc import RTCPeerConnection
        
        # 创建基本的 PeerConnection
        pc = RTCPeerConnection()
        print("✅ 可以创建 RTCPeerConnection")
        
        # 测试创建 offer
        import asyncio
        
        async def test_offer():
            try:
                offer = await pc.createOffer()
                print("✅ 可以创建 SDP offer")
                await pc.close()
                return True
            except Exception as e:
                print(f"❌ 创建 offer 失败: {e}")
                return False
        
        result = asyncio.run(test_offer())
        return result
        
    except Exception as e:
        print(f"❌ WebRTC 基本功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("WebRTC 依赖测试")
    print("=" * 30)
    
    if test_aiortc():
        print("\n基本功能测试:")
        test_basic_webrtc()
    
    print("\n测试完成!")
