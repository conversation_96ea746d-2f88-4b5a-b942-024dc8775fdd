#!/usr/bin/env python3
"""
安装必要的依赖包
"""
import subprocess
import sys

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        return False

def main():
    """安装所有必要的依赖"""
    packages = [
        "aiortc",
        "websockets",
        "fastapi[all]",
        "opencv-python",
        "uvicorn"
    ]
    
    print("Installing dependencies for WebRTC and HLS support...")
    
    success_count = 0
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print(f"\nInstallation completed: {success_count}/{len(packages)} packages installed successfully")
    
    if success_count == len(packages):
        print("✅ All dependencies installed successfully!")
    else:
        print("⚠️ Some packages failed to install. Please check the errors above.")

if __name__ == "__main__":
    main()
