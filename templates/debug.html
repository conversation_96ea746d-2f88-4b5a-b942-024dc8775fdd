<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Stream Debug</title>
    <link rel="stylesheet" href="/static/styles.css">
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <script>
        // 全局状态
        const APP_STATE = {
            activeStreams: {},
            selectedProtocol: 'mjpeg'
        };

        // 初始化页面
        document.addEventListener('DOMContentLoaded', async function() {
            // 获取摄像头列表
            const response = await fetch('/api/cameras');
            const cameras = await response.json();

            // 渲染摄像头选择器
            renderCameraSelector(cameras);

            // 设置协议切换事件
            document.getElementById('protocol-selector').addEventListener('change', function(e) {
                APP_STATE.selectedProtocol = e.target.value;
                updateActiveStreams();
            });

            // 设置添加测试摄像头按钮
            document.getElementById('add-test-camera').addEventListener('click', addTestCamera);
        });

        // 渲染摄像头选择器
        function renderCameraSelector(cameras) {
            const container = document.getElementById('camera-container');
            container.innerHTML = '';

            cameras.forEach(camera => {
                const cameraCard = document.createElement('div');
                cameraCard.className = 'camera-card';
                cameraCard.innerHTML = `
                    <div class="camera-header">
                        <h3>${camera.name} (ID: ${camera.id})</h3>
                        <div class="camera-status">
                            <span class="status-indicator" id="status-${camera.id}"></span>
                            <span class="status-text" id="status-text-${camera.id}">Offline</span>
                        </div>
                    </div>
                    <div class="video-container">
                        <img id="video-${camera.id}" class="video-stream" alt="Camera Stream" style="display: none;">
                        <video id="video-element-${camera.id}" class="video-stream" controls playsinline style="display: none;"></video>
                        <div class="controls">
                            <button class="control-btn" onclick="startStream(${camera.id})">Start</button>
                            <button class="control-btn" onclick="stopStream(${camera.id})">Stop</button>
                            <button class="control-btn" onclick="takeSnapshot(${camera.id})">Snapshot</button>
                        </div>
                    </div>
                    <div class="camera-info">
                        <p><strong>RTSP URL:</strong> ${camera.rtsp_url}</p>
                        <p><strong>Status:</strong> ${camera.is_active ? 'Active' : 'Inactive'}</p>
                    </div>
                `;
                container.appendChild(cameraCard);
            });
        }

        // 启动视频流
        function startStream(cameraId) {
            if (APP_STATE.activeStreams[cameraId]) {
                stopStream(cameraId);
            }

            const videoElement = document.getElementById(`video-${cameraId}`);
            const statusIndicator = document.getElementById(`status-${cameraId}`);
            const statusText = document.getElementById(`status-text-${cameraId}`);

            // 更新状态
            statusIndicator.className = 'status-indicator connecting';
            statusText.textContent = 'Connecting...';

            switch(APP_STATE.selectedProtocol) {
                case 'mjpeg':
                    startMjpegStream(cameraId, videoElement, statusIndicator, statusText);
                    break;
                case 'webrtc':
                    startWebrtcStream(cameraId, videoElement, statusIndicator, statusText);
                    break;
                case 'hls':
                    startHlsStream(cameraId, videoElement, statusIndicator, statusText);
                    break;
            }
        }

        // 启动MJPEG流
        function startMjpegStream(cameraId, videoElement, statusIndicator, statusText) {
            // 使用img元素显示MJPEG流
            const streamUrl = `/stream/${cameraId}/mjpeg`;
            const imgElement = document.getElementById(`video-${cameraId}`);
            const videoElementForOthers = document.getElementById(`video-element-${cameraId}`);

            // 隐藏video元素，显示img元素
            videoElementForOthers.style.display = 'none';
            imgElement.style.display = 'block';

            imgElement.onload = function() {
                statusIndicator.className = 'status-indicator active';
                statusText.textContent = 'Active (MJPEG)';
            };

            imgElement.onerror = function() {
                statusIndicator.className = 'status-indicator error';
                statusText.textContent = 'Connection Error';
                console.error('MJPEG stream error for camera', cameraId);
            };

            // 设置连接状态
            statusIndicator.className = 'status-indicator connecting';
            statusText.textContent = 'Connecting...';

            // 设置图片源为MJPEG流
            imgElement.src = streamUrl;

            APP_STATE.activeStreams[cameraId] = {
                type: 'mjpeg',
                element: imgElement
            };
        }

        // 启动HLS流
        function startHlsStream(cameraId, videoElement, statusIndicator, statusText) {
            const streamUrl = `/stream/${cameraId}/hls.m3u8`;
            const imgElement = document.getElementById(`video-${cameraId}`);
            const videoElementForHLS = document.getElementById(`video-element-${cameraId}`);

            // 隐藏img元素，显示video元素
            imgElement.style.display = 'none';
            videoElementForHLS.style.display = 'block';

            if (Hls.isSupported()) {
                const hls = new Hls();
                hls.loadSource(streamUrl);
                hls.attachMedia(videoElementForHLS);

                hls.on(Hls.Events.MANIFEST_PARSED, function() {
                    videoElementForHLS.play().then(() => {
                        statusIndicator.className = 'status-indicator active';
                        statusText.textContent = 'Active (HLS)';
                    }).catch(e => {
                        console.error('Playback error:', e);
                        statusIndicator.className = 'status-indicator error';
                        statusText.textContent = 'Playback Error';
                    });
                });

                hls.on(Hls.Events.ERROR, function(event, data) {
                    console.error('HLS error:', data);
                    statusIndicator.className = 'status-indicator error';
                    statusText.textContent = `Error: ${data.type}`;
                });

                APP_STATE.activeStreams[cameraId] = {
                    type: 'hls',
                    player: hls
                };
            } else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
                // Safari原生支持
                videoElement.src = streamUrl;
                videoElement.addEventListener('loadedmetadata', function() {
                    videoElement.play().then(() => {
                        statusIndicator.className = 'status-indicator active';
                        statusText.textContent = 'Active (HLS)';
                    }).catch(e => {
                        console.error('Playback error:', e);
                        statusIndicator.className = 'status-indicator error';
                        statusText.textContent = 'Playback Error';
                    });
                });

                APP_STATE.activeStreams[cameraId] = {
                    type: 'hls',
                    player: null
                };
            } else {
                alert('HLS not supported in this browser');
                statusIndicator.className = 'status-indicator error';
                statusText.textContent = 'HLS Not Supported';
            }
        }

        // 启动WebRTC流
        function startWebrtcStream(cameraId, videoElement, statusIndicator, statusText) {
            const imgElement = document.getElementById(`video-${cameraId}`);
            const videoElementForWebRTC = document.getElementById(`video-element-${cameraId}`);

            // 隐藏img元素，显示video元素
            imgElement.style.display = 'none';
            videoElementForWebRTC.style.display = 'block';

            // 创建WebRTC连接
            const pc = new RTCPeerConnection({
                iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
            });

            // WebSocket连接
            const wsUrl = `ws://127.0.0.1:8000/stream/${cameraId}/webrtc`;
            const ws = new WebSocket(wsUrl);

            ws.onopen = function() {
                statusIndicator.className = 'status-indicator connecting';
                statusText.textContent = 'Connecting (WebRTC)...';
            };

            ws.onmessage = async function(event) {
                try {
                    const message = JSON.parse(event.data);

                    if (message.type === 'answer') {
                        await pc.setRemoteDescription(new RTCSessionDescription({
                            type: 'answer',
                            sdp: message.sdp
                        }));
                    } else if (message.type === 'error') {
                        statusIndicator.className = 'status-indicator error';
                        statusText.textContent = 'WebRTC Error';
                        console.error('WebRTC server error:', message.message);
                    }
                } catch (e) {
                    console.error('WebRTC message error:', e);
                }
            };

            ws.onerror = function(error) {
                console.error('WebSocket error:', error);
                statusIndicator.className = 'status-indicator error';
                statusText.textContent = 'Connection Error';
            };

            pc.ontrack = function(event) {
                if (event.track.kind === 'video') {
                    videoElementForWebRTC.srcObject = event.streams[0];
                    videoElementForWebRTC.play().then(() => {
                        statusIndicator.className = 'status-indicator active';
                        statusText.textContent = 'Active (WebRTC)';
                    }).catch(e => {
                        console.error('Playback error:', e);
                        statusIndicator.className = 'status-indicator error';
                        statusText.textContent = 'Playback Error';
                    });
                }
            };

            pc.onicecandidate = function(event) {
                if (event.candidate && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                        type: 'ice',
                        candidate: event.candidate
                    }));
                }
            };

            // 创建并发送offer
            pc.createOffer().then(offer => {
                return pc.setLocalDescription(offer);
            }).then(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                        type: 'offer',
                        sdp: pc.localDescription.sdp
                    }));
                }
            }).catch(error => {
                console.error('WebRTC error:', error);
                statusIndicator.className = 'status-indicator error';
                statusText.textContent = 'Connection Error';
            });

            APP_STATE.activeStreams[cameraId] = {
                type: 'webrtc',
                pc: pc,
                ws: ws
            };
        }

        // 停止视频流
        function stopStream(cameraId) {
            const streamInfo = APP_STATE.activeStreams[cameraId];
            if (!streamInfo) return;

            const statusIndicator = document.getElementById(`status-${cameraId}`);
            const statusText = document.getElementById(`status-text-${cameraId}`);

            switch(streamInfo.type) {
                case 'mjpeg':
                    // MJPEG流通过移除src自动停止
                    break;
                case 'hls':
                    if (streamInfo.player) {
                        streamInfo.player.destroy();
                    }
                    break;
                case 'webrtc':
                    streamInfo.pc.close();
                    break;
            }

            // 清除视频源
            const imgElement = document.getElementById(`video-${cameraId}`);
            const videoElement = document.getElementById(`video-element-${cameraId}`);

            // 清除img元素
            imgElement.src = '';
            imgElement.style.display = 'none';

            // 清除video元素
            videoElement.pause();
            videoElement.srcObject = null;
            videoElement.src = '';
            videoElement.style.display = 'none';

            // 更新状态
            statusIndicator.className = 'status-indicator offline';
            statusText.textContent = 'Offline';

            delete APP_STATE.activeStreams[cameraId];
        }

        // 更新所有活动流
        function updateActiveStreams() {
            Object.keys(APP_STATE.activeStreams).forEach(cameraId => {
                stopStream(parseInt(cameraId));
                startStream(parseInt(cameraId));
            });
        }

        // 获取快照
        async function takeSnapshot(cameraId) {
            const response = await fetch(`/snapshot/${cameraId}`);
            if (!response.ok) {
                alert('Failed to get snapshot');
                return;
            }

            const blob = await response.blob();
            const url = URL.createObjectURL(blob);

            // 在新窗口显示快照
            const win = window.open();
            win.document.write(`
                <html>
                    <head><title>Snapshot Camera ${cameraId}</title></head>
                    <body style="margin:0;background:#333;display:flex;justify-content:center;align-items:center;height:100vh;">
                        <img src="${url}" style="max-width:100%;max-height:100%;">
                    </body>
                </html>
            `);
        }

        // 添加测试摄像头
        async function addTestCamera() {
            const cameraId = Math.floor(Math.random() * 1000) + 100;
            const testUrl = `rtsp://testserver.com/camera/${cameraId}`;

            const response = await fetch('/api/cameras', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    id: cameraId,
                    name: `Test Camera ${cameraId}`,
                    rtsp_url: testUrl
                })
            });

            if (response.ok) {
                const result = await response.json();
                alert(`Test camera added with ID: ${result.camera_id}`);

                // 刷新摄像头列表
                const camerasResponse = await fetch('/api/cameras');
                const cameras = await camerasResponse.json();
                renderCameraSelector(cameras);
            } else {
                alert('Failed to add test camera');
            }
        }
    </script>
</head>
<body>
    <header>
        <h1>Camera Stream Debugger</h1>
        <div class="controls">
            <label for="protocol-selector">Stream Protocol:</label>
            <select id="protocol-selector">
                <option value="mjpeg" selected>MJPEG</option>
                <option value="webrtc">WebRTC</option>
                <option value="hls">HLS</option>
            </select>
            <button id="add-test-camera">Add Test Camera</button>
        </div>
    </header>

    <main>
        <div id="camera-container" class="camera-grid">
            <!-- 摄像头卡片将通过JS动态生成 -->
        </div>
    </main>

    <footer>
        <p>FastAPI Video Streaming Service - Debug Interface</p>
        <p>Current Time: <span id="current-time"></span></p>
    </footer>

    <script>
        // 更新时间显示
        function updateTime() {
            document.getElementById('current-time').textContent = new Date().toLocaleString();
        }
        setInterval(updateTime, 1000);
        updateTime();
    </script>
</body>
</html>