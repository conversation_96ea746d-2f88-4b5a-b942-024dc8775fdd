<!DOCTYPE html>
<html>
<head>
    <title>WebRTC Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        video { width: 100%; max-width: 640px; height: 480px; background: #000; }
        .controls { margin: 20px 0; }
        button { padding: 10px 20px; margin: 5px; }
        .log { background: #f0f0f0; padding: 10px; height: 200px; overflow-y: scroll; font-family: monospace; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .status.connecting { background: #fff3cd; }
        .status.connected { background: #d4edda; }
        .status.error { background: #f8d7da; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebRTC 连接测试</h1>
        
        <div class="status" id="status">等待连接...</div>
        
        <video id="video" controls autoplay playsinline></video>
        
        <div class="controls">
            <button onclick="startWebRTC()">开始 WebRTC</button>
            <button onclick="stopWebRTC()">停止 WebRTC</button>
            <button onclick="clearLog()">清除日志</button>
        </div>
        
        <h3>连接日志:</h3>
        <div class="log" id="log"></div>
    </div>

    <script>
        let pc = null;
        let ws = null;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(message, type = 'connecting') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        async function startWebRTC() {
            log('开始 WebRTC 连接...');
            updateStatus('正在连接...', 'connecting');
            
            // 创建 RTCPeerConnection
            pc = new RTCPeerConnection({
                iceServers: [
                    { urls: 'stun:stun.l.google.com:19302' },
                    { urls: 'stun:stun1.l.google.com:19302' }
                ]
            });
            
            // 设置事件处理器
            pc.ontrack = function(event) {
                log('收到视频轨道');
                const video = document.getElementById('video');
                video.srcObject = event.streams[0];
                updateStatus('视频流已连接', 'connected');
            };
            
            pc.onicecandidate = function(event) {
                if (event.candidate && ws && ws.readyState === WebSocket.OPEN) {
                    log('发送 ICE 候选');
                    ws.send(JSON.stringify({
                        type: 'ice',
                        candidate: {
                            candidate: event.candidate.candidate,
                            sdpMid: event.candidate.sdpMid,
                            sdpMLineIndex: event.candidate.sdpMLineIndex
                        }
                    }));
                } else if (!event.candidate) {
                    log('ICE 收集完成');
                }
            };
            
            pc.onconnectionstatechange = function() {
                log(`连接状态: ${pc.connectionState}`);
                if (pc.connectionState === 'connected') {
                    updateStatus('WebRTC 连接成功', 'connected');
                } else if (pc.connectionState === 'failed') {
                    updateStatus('WebRTC 连接失败', 'error');
                }
            };
            
            // 创建 WebSocket 连接
            ws = new WebSocket('ws://127.0.0.1:8000/stream/1/webrtc');
            
            ws.onopen = async function() {
                log('WebSocket 连接已建立');
                
                try {
                    // 创建 offer
                    const offer = await pc.createOffer();
                    await pc.setLocalDescription(offer);
                    
                    log('发送 SDP offer');
                    ws.send(JSON.stringify({
                        type: 'offer',
                        sdp: pc.localDescription.sdp
                    }));
                } catch (error) {
                    log(`创建 offer 失败: ${error}`);
                    updateStatus('创建 offer 失败', 'error');
                }
            };
            
            ws.onmessage = async function(event) {
                try {
                    const message = JSON.parse(event.data);
                    log(`收到消息: ${message.type}`);
                    
                    if (message.type === 'answer') {
                        log('收到 SDP answer');
                        await pc.setRemoteDescription(new RTCSessionDescription({
                            type: 'answer',
                            sdp: message.sdp
                        }));
                        log('设置远程描述成功');
                    } else if (message.type === 'error') {
                        log(`服务器错误: ${message.message}`);
                        updateStatus(`服务器错误: ${message.message}`, 'error');
                    }
                } catch (error) {
                    log(`处理消息失败: ${error}`);
                }
            };
            
            ws.onerror = function(error) {
                log(`WebSocket 错误: ${error}`);
                updateStatus('WebSocket 连接错误', 'error');
            };
            
            ws.onclose = function() {
                log('WebSocket 连接已关闭');
                updateStatus('连接已断开', 'error');
            };
        }
        
        function stopWebRTC() {
            log('停止 WebRTC 连接');
            
            if (pc) {
                pc.close();
                pc = null;
            }
            
            if (ws) {
                ws.close();
                ws = null;
            }
            
            const video = document.getElementById('video');
            video.srcObject = null;
            
            updateStatus('连接已停止', 'connecting');
        }
    </script>
</body>
</html>
