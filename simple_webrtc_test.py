#!/usr/bin/env python3
"""
简单的 WebRTC 测试，不依赖复杂的 ICE 处理
"""
import asyncio
import json
import logging
from fastapi import FastAP<PERSON>, WebSocket
from fastapi.responses import HTMLResponse

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI()

# 检查 aiortc 可用性
try:
    from aiortc import RTCPeerConnection, RTCSessionDescription
    from aiortc.contrib.media import MediaPlayer
    WEBRTC_AVAILABLE = True
    logger.info("✅ aiortc 可用")
except ImportError:
    WEBRTC_AVAILABLE = False
    logger.error("❌ aiortc 不可用")

@app.get("/")
async def get_test_page():
    """简单的测试页面"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>简单 WebRTC 测试</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            video { width: 640px; height: 480px; background: #000; }
            .log { background: #f0f0f0; padding: 10px; height: 300px; overflow-y: scroll; font-family: monospace; }
        </style>
    </head>
    <body>
        <h1>简单 WebRTC 测试</h1>
        <video id="video" controls autoplay playsinline></video>
        <br><br>
        <button onclick="startTest()">开始测试</button>
        <button onclick="stopTest()">停止测试</button>
        <br><br>
        <div class="log" id="log"></div>

        <script>
            let pc = null;
            let ws = null;
            
            function log(msg) {
                const logDiv = document.getElementById('log');
                logDiv.innerHTML += new Date().toLocaleTimeString() + ': ' + msg + '\\n';
                logDiv.scrollTop = logDiv.scrollHeight;
                console.log(msg);
            }
            
            async function startTest() {
                log('开始 WebRTC 测试...');
                
                // 创建 PeerConnection（简化配置）
                pc = new RTCPeerConnection({
                    iceServers: []  // 不使用 STUN 服务器，简化测试
                });
                
                pc.ontrack = function(event) {
                    log('收到视频轨道');
                    document.getElementById('video').srcObject = event.streams[0];
                };
                
                pc.onconnectionstatechange = function() {
                    log('连接状态: ' + pc.connectionState);
                };
                
                // 不处理 ICE 候选，简化测试
                pc.onicecandidate = function(event) {
                    if (event.candidate) {
                        log('生成了 ICE 候选，但跳过发送');
                    } else {
                        log('ICE 收集完成');
                    }
                };
                
                // WebSocket 连接
                ws = new WebSocket('ws://127.0.0.1:8001/test-webrtc');
                
                ws.onopen = async function() {
                    log('WebSocket 连接成功');
                    
                    try {
                        const offer = await pc.createOffer();
                        await pc.setLocalDescription(offer);
                        
                        ws.send(JSON.stringify({
                            type: 'offer',
                            sdp: pc.localDescription.sdp
                        }));
                        log('发送了 SDP offer');
                    } catch (error) {
                        log('创建 offer 失败: ' + error);
                    }
                };
                
                ws.onmessage = async function(event) {
                    const message = JSON.parse(event.data);
                    log('收到消息: ' + message.type);
                    
                    if (message.type === 'answer') {
                        await pc.setRemoteDescription(new RTCSessionDescription({
                            type: 'answer',
                            sdp: message.sdp
                        }));
                        log('设置远程描述成功');
                    } else if (message.type === 'error') {
                        log('服务器错误: ' + message.message);
                    }
                };
                
                ws.onerror = function(error) {
                    log('WebSocket 错误: ' + error);
                };
                
                ws.onclose = function() {
                    log('WebSocket 连接关闭');
                };
            }
            
            function stopTest() {
                log('停止测试');
                if (pc) pc.close();
                if (ws) ws.close();
                document.getElementById('video').srcObject = null;
            }
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.websocket("/test-webrtc")
async def simple_webrtc_test(websocket: WebSocket):
    """简化的 WebRTC 测试端点"""
    if not WEBRTC_AVAILABLE:
        await websocket.close(code=1011)
        return
    
    await websocket.accept()
    logger.info("WebSocket 连接已建立")
    
    pc = RTCPeerConnection()
    
    try:
        # 添加测试视频轨道（使用测试模式）
        try:
            # 尝试使用 RTSP 流
            player = MediaPlayer("rtsp://127.0.0.1:8554/stream", format='rtsp')
            if player.video:
                pc.addTrack(player.video)
                logger.info("添加了 RTSP 视频轨道")
            else:
                # 如果 RTSP 不可用，使用测试模式
                from aiortc.contrib.media import MediaPlayer
                player = MediaPlayer("testsrc=size=640x480:rate=30", format="lavfi")
                pc.addTrack(player.video)
                logger.info("添加了测试视频轨道")
        except Exception as e:
            logger.error(f"添加视频轨道失败: {e}")
            await websocket.send_json({"type": "error", "message": "无法添加视频轨道"})
            return
        
        # 处理消息（不处理 ICE 候选）
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)
                
                if message.get("type") == "offer":
                    logger.info("收到 SDP offer")
                    
                    # 设置远程描述
                    offer = RTCSessionDescription(sdp=message["sdp"], type="offer")
                    await pc.setRemoteDescription(offer)
                    
                    # 创建并发送 answer
                    answer = await pc.createAnswer()
                    await pc.setLocalDescription(answer)
                    
                    await websocket.send_json({
                        "type": "answer",
                        "sdp": pc.localDescription.sdp
                    })
                    logger.info("发送了 SDP answer")
                    
            except Exception as e:
                logger.error(f"处理消息错误: {e}")
                break
                
    except Exception as e:
        logger.error(f"WebRTC 错误: {e}")
    finally:
        await pc.close()
        logger.info("WebRTC 连接已关闭")

if __name__ == "__main__":
    import uvicorn
    print("启动简单 WebRTC 测试服务器...")
    print("访问: http://127.0.0.1:8001")
    uvicorn.run(app, host="127.0.0.1", port=8001)
