import os
import subprocess
import time
import logging
import cv2
import numpy as np
from aiortc import RTCPeerConnection, RTCSessionDescription, VideoStreamTrack
from aiortc.contrib.media import MediaPlayer
from fastapi import WebSocket, status
from camera_manager import CameraManager


class StreamProcessor:
    def __init__(self, camera_manager: CameraManager):
        self.camera_manager = camera_manager
        self.active_streams = {}
        self.logger = logging.getLogger("StreamProcessor")
        self.snapshots = {}

        # 用于存储WebRTC对等连接
        self.webrtc_peers = {}

    def generate_mjpeg_stream(self, camera_id: int):
        """生成MJPEG视频流"""
        camera = self.camera_manager.get_camera(camera_id)
        if not camera:
            self.logger.error(f"Camera {camera_id} not found")
            return

        # 启动新的FFmpeg进程
        command = self._build_ffmpeg_command(camera.rtsp_url)
        self.logger.info(f"Starting FFmpeg with command: {' '.join(command)}")

        try:
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                bufsize=0
            )
            self.logger.info(f"Started new stream for camera {camera_id}")

            # 读取MJPEG流并逐帧发送
            buffer = b''
            while True:
                # 读取数据块
                chunk = process.stdout.read(4096)
                if not chunk:
                    self.logger.warning(f"Stream ended for camera {camera_id}")
                    break

                buffer += chunk

                # 查找JPEG帧边界
                while True:
                    # 查找JPEG开始标记 (FF D8)
                    start_marker = buffer.find(b'\xff\xd8')
                    if start_marker == -1:
                        break

                    # 查找JPEG结束标记 (FF D9)
                    end_marker = buffer.find(b'\xff\xd9', start_marker)
                    if end_marker == -1:
                        break

                    # 提取完整的JPEG帧
                    frame = buffer[start_marker:end_marker + 2]
                    buffer = buffer[end_marker + 2:]

                    # 生成MJPEG帧
                    yield (
                        b'--frame\r\n'
                        b'Content-Type: image/jpeg\r\n'
                        b'Content-Length: ' + str(len(frame)).encode() + b'\r\n\r\n' +
                        frame + b'\r\n'
                    )

                    # 存储快照
                    self.snapshots[camera_id] = frame

        except Exception as e:
            self.logger.error(f"Error in MJPEG stream for camera {camera_id}: {str(e)}")
        finally:
            try:
                if 'process' in locals():
                    process.terminate()
                    process.wait(timeout=5)
            except:
                pass
            self.logger.info(f"Terminated stream for camera {camera_id}")

    def get_snapshot(self, camera_id: int) -> bytes:
        """获取最新快照"""
        return self.snapshots.get(camera_id, b'')

    # async def handle_webrtc_connection(self, websocket: WebSocket, camera_id: int):
    #     """处理WebRTC连接"""
    #     camera = self.camera_manager.get_camera(camera_id)
    #     if not camera:
    #         await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
    #         return
    #
    #     peer_id = f"{camera_id}-{id(websocket)}"
    #     pc = RTCPeerConnection()
    #     self.webrtc_peers[peer_id] = pc
    #     self.logger.info(f"New WebRTC connection for camera {camera_id}, peer: {peer_id}")

        # 添加视频轨道
        # @pc.on("track")
        # def on_track(track):
        #     self.logger.info(f"Track {track.kind} received")
        #
        # # 添加摄像头媒体源
        # player = MediaPlayer(camera.rtsp_url)
        # if player.video:
        #     pc.addTrack(player.video)
        #
        # # 处理信令消息
        # try:
        #     while True:
        #         data = await websocket.receive_text()
        #         message = data
        #
        #         if isinstance(message, str) and message.startswith("offer"):
        #             # 处理SDP offer
        #             offer = RTCSessionDescription(sdp=message["sdp"], type=message["type"])
        #             await pc.setRemoteDescription(offer)
        #
        #             # 创建并发送answer
        #             answer = await pc.createAnswer()
        #             await pc.setLocalDescription(answer)
        #             await websocket.send_json({
        #                 "type": "answer",
        #                 "sdp": pc.localDescription.sdp
        #             })
        #         elif isinstance(message, dict) and message.get("type") == "ice":
        #             # 处理ICE候选
        #             await pc.addIceCandidate(message["candidate"])
        # except Exception as e:
        #     self.logger.error(f"WebRTC connection error: {str(e)}")
        # finally:
        #     await pc.close()
        #     del self.webrtc_peers[peer_id]
        #     self.logger.info(f"WebRTC connection closed for peer {peer_id}")

    def _build_ffmpeg_command(self, rtsp_url: str):
        """构建FFmpeg命令"""
        return [
            "ffmpeg",
            "-rtsp_transport", "tcp",  # 强制使用TCP传输
            "-i", rtsp_url,  # 输入RTSP流
            "-loglevel", "error",  # 只显示错误日志
            "-an",  # 禁用音频
            "-c:v", "mjpeg",  # 使用MJPEG编码
            "-q:v", "5",  # 视频质量 (1-31, 1=最高)
            "-vf", "fps=15,scale=1280:720",  # 限制帧率和分辨率
            "-f", "mpjpeg",  # 输出格式
            "-"  # 输出到stdout
        ]

    def _get_cv2_capture(self, camera_id: int):
        """获取OpenCV视频捕获对象（用于调试）"""
        camera = self.camera_manager.get_camera(camera_id)
        if not camera:
            return None

        # 使用OpenCV捕获视频（仅用于调试，生产环境不建议）
        cap = cv2.VideoCapture(camera.rtsp_url)
        if not cap.isOpened():
            self.logger.error(f"Failed to open RTSP stream: {camera.rtsp_url}")
            return None

        # 设置缓冲区大小
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        return cap