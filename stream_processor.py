import os
import subprocess
import time
import logging
import cv2
import numpy as np
from fastapi import WebSocket, status
from camera_manager import CameraManager

# 可选导入 aiortc
try:
    from aiortc import RTCPeerConnection, RTCSessionDescription, VideoStreamTrack
    from aiortc.contrib.media import MediaPlayer
    WEBRTC_AVAILABLE = True
except ImportError:
    WEBRTC_AVAILABLE = False
    RTCPeerConnection = None
    RTCSessionDescription = None
    VideoStreamTrack = None
    MediaPlayer = None


class StreamProcessor:
    def __init__(self, camera_manager: CameraManager):
        self.camera_manager = camera_manager
        self.active_streams = {}
        self.logger = logging.getLogger("StreamProcessor")
        self.snapshots = {}

        # 用于存储WebRTC对等连接
        self.webrtc_peers = {}

    def generate_mjpeg_stream(self, camera_id: int):
        """生成MJPEG视频流"""
        camera = self.camera_manager.get_camera(camera_id)
        if not camera:
            self.logger.error(f"Camera {camera_id} not found")
            return

        # 首先尝试使用FFmpeg
        try:
            yield from self._generate_mjpeg_with_ffmpeg(camera_id, camera.rtsp_url)
        except FileNotFoundError:
            self.logger.warning("FFmpeg not found, falling back to OpenCV")
            yield from self._generate_mjpeg_with_opencv(camera_id, camera.rtsp_url)
        except Exception as e:
            self.logger.error(f"FFmpeg failed: {e}, falling back to OpenCV")
            yield from self._generate_mjpeg_with_opencv(camera_id, camera.rtsp_url)

    def _generate_mjpeg_with_ffmpeg(self, camera_id: int, rtsp_url: str):
        """使用FFmpeg生成MJPEG流"""
        command = self._build_ffmpeg_command(rtsp_url)
        self.logger.info(f"Starting FFmpeg with command: {' '.join(command)}")

        process = None
        try:
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                bufsize=0
            )
            self.logger.info(f"Started FFmpeg stream for camera {camera_id}")

            # 读取MJPEG流并逐帧发送
            buffer = b''
            while True:
                # 读取数据块
                chunk = process.stdout.read(4096)
                if not chunk:
                    self.logger.warning(f"FFmpeg stream ended for camera {camera_id}")
                    break

                buffer += chunk

                # 查找JPEG帧边界
                while True:
                    # 查找JPEG开始标记 (FF D8)
                    start_marker = buffer.find(b'\xff\xd8')
                    if start_marker == -1:
                        break

                    # 查找JPEG结束标记 (FF D9)
                    end_marker = buffer.find(b'\xff\xd9', start_marker)
                    if end_marker == -1:
                        break

                    # 提取完整的JPEG帧
                    frame = buffer[start_marker:end_marker + 2]
                    buffer = buffer[end_marker + 2:]

                    # 生成MJPEG帧
                    yield (
                        b'--frame\r\n'
                        b'Content-Type: image/jpeg\r\n'
                        b'Content-Length: ' + str(len(frame)).encode() + b'\r\n\r\n' +
                        frame + b'\r\n'
                    )

                    # 存储快照
                    self.snapshots[camera_id] = frame

        finally:
            if process:
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except:
                    pass
            self.logger.info(f"Terminated FFmpeg stream for camera {camera_id}")

    def _generate_mjpeg_with_opencv(self, camera_id: int, rtsp_url: str):
        """使用OpenCV生成MJPEG流"""
        self.logger.info(f"Starting OpenCV stream for camera {camera_id}")

        cap = None
        try:
            cap = cv2.VideoCapture(rtsp_url)
            if not cap.isOpened():
                self.logger.error(f"Failed to open RTSP stream with OpenCV: {rtsp_url}")
                return

            # 设置缓冲区大小
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            cap.set(cv2.CAP_PROP_FPS, 10)

            self.logger.info(f"Started OpenCV stream for camera {camera_id}")

            while True:
                ret, frame = cap.read()
                if not ret:
                    self.logger.warning(f"Failed to read frame from camera {camera_id}")
                    break

                # 调整帧大小
                frame = cv2.resize(frame, (640, 480))

                # 编码为JPEG
                encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), 80]
                result, encoded_img = cv2.imencode('.jpg', frame, encode_param)

                if not result:
                    continue

                frame_bytes = encoded_img.tobytes()

                # 生成MJPEG帧
                yield (
                    b'--frame\r\n'
                    b'Content-Type: image/jpeg\r\n'
                    b'Content-Length: ' + str(len(frame_bytes)).encode() + b'\r\n\r\n' +
                    frame_bytes + b'\r\n'
                )

                # 存储快照
                self.snapshots[camera_id] = frame_bytes

                # 控制帧率
                time.sleep(0.1)  # 10 FPS

        except Exception as e:
            self.logger.error(f"Error in OpenCV stream for camera {camera_id}: {str(e)}")
        finally:
            if cap:
                cap.release()
            self.logger.info(f"Terminated OpenCV stream for camera {camera_id}")

    def get_snapshot(self, camera_id: int) -> bytes:
        """获取最新快照"""
        return self.snapshots.get(camera_id, b'')

    async def handle_webrtc_connection(self, websocket: WebSocket, camera_id: int):
        """处理WebRTC连接"""
        if not WEBRTC_AVAILABLE:
            await websocket.send_json({"type": "error", "message": "WebRTC not available - aiortc not installed"})
            await websocket.close(code=status.WS_1011_INTERNAL_ERROR)
            return

        camera = self.camera_manager.get_camera(camera_id)
        if not camera:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return

        peer_id = f"{camera_id}-{id(websocket)}"

        try:
            pc = RTCPeerConnection()
            self.webrtc_peers[peer_id] = pc
            self.logger.info(f"New WebRTC connection for camera {camera_id}, peer: {peer_id}")

            # 添加视频轨道
            @pc.on("track")
            def on_track(track):
                self.logger.info(f"Track {track.kind} received")

            # 添加摄像头媒体源
            try:
                from aiortc.contrib.media import MediaPlayer

                # 首先尝试使用测试视频源，确保 WebRTC 基本功能正常
                try:
                    # 使用测试模式
                    player = MediaPlayer("testsrc=size=640x480:rate=15", format="lavfi")
                    if player.video:
                        pc.addTrack(player.video)
                        self.logger.info(f"Added test video track for camera {camera_id}")
                    else:
                        raise Exception("Test video source not available")
                except Exception as test_error:
                    self.logger.warning(f"Test video failed: {test_error}, trying RTSP")

                    # 如果测试视频失败，尝试 RTSP
                    player = MediaPlayer(camera.rtsp_url, format='rtsp', options={
                        'rtsp_transport': 'tcp',
                        'stimeout': '5000000'  # 5秒超时
                    })

                    if player.video:
                        pc.addTrack(player.video)
                        self.logger.info(f"Added RTSP video track for camera {camera_id}")
                    else:
                        raise Exception("No video track available from RTSP")

            except Exception as e:
                self.logger.error(f"Failed to add media player: {e}")
                # 发送错误消息
                await websocket.send_json({"type": "error", "message": f"Failed to access camera: {str(e)}"})
                return

            # 处理信令消息
            while True:
                try:
                    data = await websocket.receive_text()
                    import json
                    message = json.loads(data)
                    self.logger.info(f"Received message type: {message.get('type')}")

                    if message.get("type") == "offer":
                        self.logger.info("Processing SDP offer")
                        # 处理SDP offer
                        offer = RTCSessionDescription(sdp=message["sdp"], type="offer")
                        await pc.setRemoteDescription(offer)
                        self.logger.info("Set remote description successfully")

                        # 创建并发送answer
                        answer = await pc.createAnswer()
                        self.logger.info("Created answer successfully")

                        # 修复 SDP 方向问题
                        # 确保 answer 中的方向是正确的
                        answer_sdp = answer.sdp
                        # 强制设置为 sendonly，因为我们只发送视频
                        if "a=sendrecv" in answer_sdp:
                            answer_sdp = answer_sdp.replace("a=sendrecv", "a=sendonly")
                        elif "a=recvonly" in answer_sdp:
                            answer_sdp = answer_sdp.replace("a=recvonly", "a=sendonly")

                        # 创建修正后的 answer
                        corrected_answer = RTCSessionDescription(sdp=answer_sdp, type="answer")
                        await pc.setLocalDescription(corrected_answer)
                        self.logger.info("Set local description successfully")

                        await websocket.send_json({
                            "type": "answer",
                            "sdp": pc.localDescription.sdp
                        })
                        self.logger.info("Sent SDP answer")

                    elif message.get("type") == "ice":
                        # 处理ICE候选
                        candidate_data = message.get("candidate")
                        self.logger.info(f"Received ICE candidate: {candidate_data}")

                        if candidate_data and candidate_data != "null":
                            try:
                                from aiortc import RTCIceCandidate

                                # 检查候选数据结构
                                if isinstance(candidate_data, dict):
                                    candidate_str = candidate_data.get("candidate")
                                    sdp_mid = candidate_data.get("sdpMid")
                                    sdp_mline_index = candidate_data.get("sdpMLineIndex")

                                    self.logger.info(f"ICE candidate details - candidate: {candidate_str}, sdpMid: {sdp_mid}, sdpMLineIndex: {sdp_mline_index}")

                                    if candidate_str:
                                        candidate = RTCIceCandidate(
                                            candidate=candidate_str,
                                            sdpMid=sdp_mid,
                                            sdpMLineIndex=sdp_mline_index
                                        )
                                        await pc.addIceCandidate(candidate)
                                        self.logger.info("Successfully added ICE candidate")
                                    else:
                                        self.logger.warning("Empty candidate string")
                                else:
                                    self.logger.error(f"Invalid candidate data type: {type(candidate_data)}")

                            except Exception as e:
                                self.logger.error(f"Failed to add ICE candidate: {e}")
                                import traceback
                                self.logger.error(f"Traceback: {traceback.format_exc()}")
                        else:
                            self.logger.info("Received null ICE candidate (end of candidates)")

                except Exception as e:
                    self.logger.error(f"WebRTC message error: {str(e)}")
                    import traceback
                    self.logger.error(f"Full traceback: {traceback.format_exc()}")
                    break

        except Exception as e:
            self.logger.error(f"WebRTC connection error: {str(e)}")
        finally:
            try:
                if peer_id in self.webrtc_peers:
                    await self.webrtc_peers[peer_id].close()
                    del self.webrtc_peers[peer_id]
            except:
                pass
            self.logger.info(f"WebRTC connection closed for peer {peer_id}")

    def generate_hls_playlist(self, camera_id: int) -> str:
        """生成HLS播放列表"""
        camera = self.camera_manager.get_camera(camera_id)
        if not camera:
            return ""

        # 生成一个简单的HLS播放列表
        # 注意：这是一个简化的实现，实际生产环境需要更复杂的逻辑
        import time
        sequence = int(time.time()) % 1000

        playlist = f"""#EXTM3U
#EXT-X-VERSION:3
#EXT-X-TARGETDURATION:10
#EXT-X-MEDIA-SEQUENCE:{sequence}
#EXTINF:10.0,
segment_{sequence}.ts
#EXTINF:10.0,
segment_{sequence + 1}.ts
#EXTINF:10.0,
segment_{sequence + 2}.ts
"""
        return playlist

    def get_hls_segment(self, camera_id: int, segment_id: int) -> bytes:
        """获取HLS片段（简化实现）"""
        camera = self.camera_manager.get_camera(camera_id)
        if not camera:
            return b''

        # 简化实现：返回一个模拟的TS片段
        # 实际应用中需要真正的HLS分片器
        self.logger.warning(f"HLS segment {segment_id} requested for camera {camera_id} - returning mock data")

        # 返回一个最小的TS文件头
        # 这只是为了避免404错误，实际不包含视频数据
        ts_header = bytes([
            0x47, 0x40, 0x00, 0x10,  # TS packet header
            0x00, 0x00, 0xB0, 0x0D,  # PAT header
            0x00, 0x01, 0xC1, 0x00,  # PAT data
            0x00, 0x00, 0x01, 0xF0,
            0x00, 0x2E, 0x70, 0x19,
            # 填充到188字节
        ] + [0xFF] * (188 - 20))

        # 返回多个TS包以模拟一个片段
        return ts_header * 100  # 约18KB的模拟数据

    def _build_ffmpeg_command(self, rtsp_url: str):
        """构建FFmpeg命令"""
        return [
            "ffmpeg",
            "-rtsp_transport", "tcp",  # 强制使用TCP传输
            "-i", rtsp_url,  # 输入RTSP流
            "-loglevel", "warning",  # 显示警告和错误日志
            "-an",  # 禁用音频
            "-c:v", "mjpeg",  # 使用MJPEG编码
            "-q:v", "3",  # 视频质量 (1-31, 1=最高)
            "-vf", "fps=10,scale=640:480",  # 限制帧率和分辨率
            "-f", "mjpeg",  # 输出格式改为mjpeg
            "-"  # 输出到stdout
        ]

    def _get_cv2_capture(self, camera_id: int):
        """获取OpenCV视频捕获对象（用于调试）"""
        camera = self.camera_manager.get_camera(camera_id)
        if not camera:
            return None

        # 使用OpenCV捕获视频（仅用于调试，生产环境不建议）
        cap = cv2.VideoCapture(camera.rtsp_url)
        if not cap.isOpened():
            self.logger.error(f"Failed to open RTSP stream: {camera.rtsp_url}")
            return None

        # 设置缓冲区大小
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        return cap