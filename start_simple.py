#!/usr/bin/env python3
"""
启动简化版视频流服务（不强制要求 aiortc）
"""
import sys
import os
import logging

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """启动服务"""
    print("FastAPI Video Streaming Service")
    print("=" * 40)
    
    # 检查基础依赖
    try:
        import cv2
        print("✅ OpenCV available")
    except ImportError:
        print("❌ OpenCV not available - please install: pip install opencv-python")
        return
    
    try:
        import fastapi
        print("✅ FastAPI available")
    except ImportError:
        print("❌ FastAPI not available - please install: pip install fastapi[all]")
        return
    
    try:
        import uvicorn
        print("✅ Uvicorn available")
    except ImportError:
        print("❌ Uvicorn not available - please install: pip install uvicorn")
        return
    
    # 检查可选依赖
    try:
        import aiortc
        print("✅ WebRTC support available")
    except ImportError:
        print("⚠️ WebRTC support disabled (aiortc not installed)")
    
    print("\nStarting server...")
    print("Available protocols:")
    print("  - MJPEG: ✅ Always available")
    print("  - WebRTC: ✅ Available" if 'aiortc' in sys.modules else "  - WebRTC: ⚠️ Disabled")
    print("  - HLS: ⚠️ Requires FFmpeg")
    print("\nServer URLs:")
    print("  - Debug page: http://127.0.0.1:8000/debug")
    print("  - API docs: http://127.0.0.1:8000/docs")
    print("\nPress Ctrl+C to stop")
    print("=" * 40)
    
    try:
        import uvicorn
        from main import app
        
        uvicorn.run(
            app, 
            host="127.0.0.1", 
            port=8000, 
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n\nServer stopped")
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()
